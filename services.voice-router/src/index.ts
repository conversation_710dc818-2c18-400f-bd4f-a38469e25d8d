import { MediaServer } from './core/MediaServer';
import { SessionManager } from './services/SessionManager';
import { createHttpServer } from './api/http/server';
//import { createWebSocketServer } from './api/ws/server';
import config from './config/index';

async function main() {
  console.log('Starting Voice Router service...');

  // 1. Initialize the MediaServer to create and manage MediaSoup Workers
  const mediaServer = MediaServer.getInstance();
  await mediaServer.start({ numWorkers: config.mediasoup.numWorkers });
  console.log('MediaServer started.');

  // 2. Initialize the SessionManager to handle call state
  const sessionManager = new SessionManager(mediaServer);
  console.log('SessionManager initialized.');

  // 3. Create the HTTP server for internal orchestration
  const httpServer = createHttpServer(sessionManager);
  console.log('HTTP server created.');

  // 4. Create and attach the WebSocket server for client signaling
  //createWebSocketServer(httpServer);
  console.log('WebSocket server attached.');

  // 5. Start listening for connections
  httpServer.listen(config.http.port, config.http.host, () => {
    console.log(`--> Voice Router listening on http://${config.http.host}:${config.http.port}`);
  });
}

main().catch((error) => {
  console.error('Fatal error during startup:', error);
  process.exit(1);
});