{"name": "cortexa-voice-router", "version": "1.0.0", "description": "SFU for WebRTC voice-router service", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "watch": "tsc --watch", "clean": "rm -rf dist", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"dotenv": "^17.2.1", "express": "^4.18.2", "mediasoup": "^3.12.9", "socket.io": "^4.7.2"}, "devDependencies": {"@types/express": "^4.17.17", "@types/node": "^20.8.0", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0"}, "author": "", "license": "ISC"}